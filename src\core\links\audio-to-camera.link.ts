/**
 * Lien Audio → Caméra
 *
 * Automation intelligente qui contrôle la visibilité des caméras selon les niveaux audio des micros.
 *
 * Fonctionnalités :
 * - Activation quasi-instantanée des caméras (100ms)
 * - Maintien minimum des caméras actives (4s)
 * - Mode plein écran automatique lors de discussions multiples (3s)
 * - Protection contre les changements trop rapides (3s minimum)
 * - Support des micros multiples par caméra
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, ModuleEvent } from '../interfaces/module.interface';
import { audioToCameraConfig } from '../../config';
import type { MicToCameraMapping } from '../../config';
import { AudioSourceConfig } from '../../obs/obs-observer.service';

interface AudioEventData {
    inputName: string;
    [key: string]: any;
}

interface AudioState {
    /** Niveau audio actuel (high/low) */
    level: 'high' | 'low';
    /** Timestamp de la dernière activation */
    lastActivated: number;
    /** Timestamp de la dernière désactivation */
    lastDeactivated: number;
}

interface CameraState {
    /** Source caméra actuellement visible */
    activeCamera: string | null;
    /** Timestamp de l'activation de la caméra active */
    activatedAt: number;
    /** Timestamp du dernier changement de caméra */
    lastChangeTime: number;
    /** Mode plein écran actif */
    isFullscreen: boolean;
    /** Timestamp d'entrée en mode plein écran */
    fullscreenActivatedAt: number;
    /** Timer principal pour les actions différées */
    actionTimer: NodeJS.Timeout | null;
    /** Timer pour le mode plein écran */
    fullscreenTimer: NodeJS.Timeout | null;
}

export class AudioToCameraLink implements IModuleLink {
    public readonly name = 'audio-to-camera';
    public readonly description = 'Contrôle la visibilité des caméras selon les niveaux audio des micros';
    public readonly enabled: boolean;

    private obsModule?: IModule;
    private config = audioToCameraConfig();

    // États internes
    private audioStates = new Map<string, AudioState>();
    private cameraState: CameraState = {
        activeCamera: null,
        activatedAt: 0,
        lastChangeTime: 0,
        isFullscreen: false,
        fullscreenActivatedAt: 0,
        actionTimer: null,
        fullscreenTimer: null,
    };

    constructor(private linkConfig: LinkConfig) {
        this.enabled = linkConfig.enabled && this.config?.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        await Promise.resolve();

        if (!this.enabled) {
            console.log('[AudioToCameraLink] Link disabled - skipping initialization');
            return;
        }

        // Récupérer le module OBS
        this.obsModule = modules.get('obs');
        if (!this.obsModule) {
            console.warn('[AudioToCameraLink] OBS module not found - link will not function');
            return;
        }

        // Initialiser les états audio pour chaque micro configuré
        this.config.micToCameraMapping.forEach((mapping: MicToCameraMapping) => {
            this.audioStates.set(mapping.micName, {
                level: 'low',
                lastActivated: 0,
                lastDeactivated: 0,
            });
        });

        // Configurer les sources audio à écouter dans l'OBS observer
        this.configureAudioSources();

        // Configurer le debug audio
        this.configureAudioDebug();

        // Écouter les événements audio du module OBS
        this.setupOBSListeners();

        console.log(`[AudioToCameraLink] Initialized with ${this.config.micToCameraMapping.length} mic-to-camera mappings`);
        if (this.config.debug.verbose) {
            console.log('[AudioToCameraLink] Configuration:', {
                targetScene: this.config.targetScene,
                audioThresholdDb: this.config.audioThresholdDb,
                activationDelayMs: this.config.activationDelayMs,
                holdDurationMs: this.config.holdDurationMs,
                mappings: this.config.micToCameraMapping,
            });
        }
    }

    async cleanup(): Promise<void> {
        await Promise.resolve();

        // Nettoyer tous les timers
        this.clearAllTimers();

        console.log('[AudioToCameraLink] Cleaned up');
    }

    // ============================================================================
    // CONFIGURATION DES SOURCES AUDIO
    // ============================================================================

    /**
     * Configure les sources audio à écouter dans l'OBS observer
     */
    private configureAudioSources(): void {
        if (!this.obsModule) return;

        // Créer la liste des sources audio à partir du mapping
        const audioSources: AudioSourceConfig[] = this.config.micToCameraMapping.map((mapping: MicToCameraMapping) => ({
            inputName: mapping.micName,
        }));

        // Configurer les sources dans l'OBS observer via la méthode publique du module
        // eslint-disable-next-line
        (this.obsModule as any).configureAudioSources?.(audioSources);

        if (this.config.debug.verbose) {
            console.log(
                '[AudioToCameraLink] Configured audio sources:',
                audioSources.map((s) => `${s.inputName}`),
            );
        }
    }

    /**
     * Configure le debug audio dans l'OBS observer
     */
    private configureAudioDebug(): void {
        if (!this.obsModule) return;

        const debugConfig = {
            enabled: this.config.debug.logAudioLevels || this.config.debug.logAudioThresholds,
            logThresholds: this.config.debug.logAudioThresholds,
            intervalMs: this.config.debug.audioLogIntervalMs,
        };

        // Configurer le debug dans l'OBS observer via la méthode publique du module
        // eslint-disable-next-line
        (this.obsModule as any).configureAudioDebug?.(debugConfig);

        if (this.config.debug.verbose) {
            console.log('[AudioToCameraLink] Configured audio debug:', debugConfig);
        }
    }

    // ============================================================================
    // GESTION DES ÉVÉNEMENTS AUDIO
    // ============================================================================

    private setupOBSListeners(): void {
        if (!this.obsModule) return;

        const eventCallback = (event: ModuleEvent) => {
            const audioData = event.data as AudioEventData;
            if (event.type === 'input_volume_HIGH' && audioData?.inputName) {
                this.handleMicActivated(audioData.inputName);
            } else if (event.type === 'input_volume_LOW' && audioData?.inputName) {
                this.handleMicDeactivated(audioData.inputName);
            }
        };

        this.obsModule.onEvent(eventCallback);
    }

    private handleMicActivated(micName: string): void {
        // Trouver la caméra associée à ce micro
        const mapping = this.config.micToCameraMapping.find((m: MicToCameraMapping) => m.micName === micName);
        if (!mapping) return;

        const cameraSource = mapping.cameraSource;
        const audioState = this.audioStates.get(micName);
        if (!audioState) return;

        // Mettre à jour l'état audio
        audioState.level = 'high';
        audioState.lastActivated = Date.now();
        if (this.config.debug.verbose) {
            console.log(`[AudioToCameraLink] Mic activated: ${micName} → ${cameraSource}`);
        }

        // Annuler le timer d'action en cours s'il existe
        if (this.cameraState.actionTimer) {
            clearTimeout(this.cameraState.actionTimer);
            this.cameraState.actionTimer = null;
        }

        // Évaluer la situation avec la nouvelle logique
        this.evaluateCameraState();
    }

    private handleMicDeactivated(micName: string): void {
        // Mettre à jour l'état audio
        const audioState = this.audioStates.get(micName);
        if (audioState) {
            audioState.level = 'low';
            audioState.lastDeactivated = Date.now();
        }

        if (this.config.debug.verbose) {
            console.log(`[AudioToCameraLink] Mic deactivated: ${micName}`);
        }

        // Évaluer la situation avec la nouvelle logique
        this.evaluateCameraState();
    }

    // ============================================================================
    // LOGIQUE PRINCIPALE DE DÉCISION
    // ============================================================================

    /**
     * Évaluer l'état des caméras et décider de l'action à prendre - Version simplifiée
     */
    private evaluateCameraState(): void {
        // Grouper les micros actifs par caméra
        const activeCameraGroups = this.getActiveCameraGroups();

        if (this.config.debug.verbose) {
            console.log(`[AudioToCameraLink] Active camera groups:`, Array.from(activeCameraGroups.entries()));
        }

        // Gérer le mode plein écran
        this.handleFullscreenMode(activeCameraGroups);

        // Si on est en mode plein écran, ne pas changer de caméra
        if (this.cameraState.isFullscreen) {
            return;
        }

        const now = Date.now();

        if (activeCameraGroups.size === 0) {
            // Aucun micro actif - programmer le masquage si configuré
            if (this.config.fallbackBehavior.hideAllWhenInactive) {
                this.scheduleAction(
                    () => {
                        void this.hideAllCameras();
                    },
                    this.config.fallbackBehavior.hideDelayMs,
                    'hide_all',
                );
            }
        } else if (activeCameraGroups.size === 1) {
            // Une seule caméra a des micros actifs
            const [cameraSource] = activeCameraGroups.keys();

            // Vérifier si on peut changer de caméra
            const canChange = this.canChangeCamera(cameraSource, now);

            if (canChange.allowed) {
                // Déterminer le délai d'activation
                const delay = this.cameraState.activeCamera === null ? this.config.activationDelayMs : 0;

                this.scheduleAction(
                    () => {
                        void this.activateCamera(cameraSource);
                    },
                    delay,
                    `activate_${cameraSource}`,
                );
            } else if (canChange.retryAfter > 0) {
                // Programmer une réévaluation plus tard
                this.scheduleAction(
                    () => {
                        this.evaluateCameraState();
                    },
                    canChange.retryAfter,
                    'reevaluate',
                );
            }
        }
        // Si activeCameraGroups.size > 1, la logique du mode plein écran s'en occupe
    }

    // ============================================================================
    // CONTRÔLE DES CAMÉRAS
    // ============================================================================

    private async activateCamera(cameraSource: string): Promise<void> {
        if (!this.obsModule) return;

        try {
            // Masquer toutes les caméras d'abord
            const allCameraSources = this.config.micToCameraMapping.map((m: MicToCameraMapping) => m.cameraSource);
            // eslint-disable-next-line
            await (this.obsModule as any).hideAllSources(this.config.targetScene, allCameraSources);

            // Afficher la caméra sélectionnée
            // eslint-disable-next-line
            await (this.obsModule as any).setSourceVisibility(this.config.targetScene, cameraSource, true);

            // Mettre à jour l'état
            const now = Date.now();
            this.cameraState.activeCamera = cameraSource;
            this.cameraState.activatedAt = now;
            this.cameraState.lastChangeTime = now;
            this.cameraState.actionTimer = null;

            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Camera activated: ${cameraSource} in scene ${this.config.targetScene}`);
        } catch (error) {
            console.error(`[AudioToCameraLink] Failed to activate camera ${cameraSource}:`, error);
        }
    }

    private async hideAllCameras(): Promise<void> {
        if (!this.obsModule) return;

        try {
            const allCameraSources = this.config.micToCameraMapping.map((m: MicToCameraMapping) => m.cameraSource);
            // eslint-disable-next-line
            await (this.obsModule as any).hideAllSources(this.config.targetScene, allCameraSources);

            this.cameraState.activeCamera = null;
            this.cameraState.activatedAt = 0;
            this.cameraState.lastChangeTime = Date.now();
            this.cameraState.actionTimer = null;

            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] All cameras hidden in scene ${this.config.targetScene} (fullscreen mode)`);
        } catch (error) {
            console.error('[AudioToCameraLink] Failed to hide cameras:', error);
        }
    }

    // ============================================================================
    // LOGIQUE SIMPLIFIÉE DE TIMING
    // ============================================================================

    /**
     * Vérifier si on peut changer de caméra selon les contraintes de timing
     */
    private canChangeCamera(targetCamera: string, now: number): { allowed: boolean; retryAfter: number } {
        // Si c'est la même caméra, pas besoin de changer
        if (this.cameraState.activeCamera === targetCamera) {
            return { allowed: false, retryAfter: 0 };
        }

        // 1. Vérifier la durée de maintien si une autre caméra est déjà active
        if (this.cameraState.activeCamera && now - this.cameraState.activatedAt < this.config.holdDurationMs) {
            const remainingHold = this.config.holdDurationMs - (now - this.cameraState.activatedAt);
            if (this.config.debug.verbose) {
                console.log(`[AudioToCameraLink] Camera ${this.cameraState.activeCamera} still in hold period (${remainingHold}ms remaining)`);
            }
            return { allowed: false, retryAfter: remainingHold + 100 };
        }

        // 2. Vérifier le délai minimum entre changements
        if (this.cameraState.lastChangeTime > 0 && now - this.cameraState.lastChangeTime < this.config.minChangeIntervalMs) {
            const remainingInterval = this.config.minChangeIntervalMs - (now - this.cameraState.lastChangeTime);
            if (this.config.debug.verbose) {
                console.log(`[AudioToCameraLink] Too soon since last change (${remainingInterval}ms remaining)`);
            }
            return { allowed: false, retryAfter: remainingInterval + 100 };
        }

        return { allowed: true, retryAfter: 0 };
    }

    /**
     * Programmer une action avec un délai - Remplace tous les anciens timers
     */
    private scheduleAction(action: () => void, delayMs: number, actionName: string): void {
        // Annuler l'action précédente s'il y en a une
        if (this.cameraState.actionTimer) {
            clearTimeout(this.cameraState.actionTimer);
            this.cameraState.actionTimer = null;
        }

        if (this.config.debug.verbose) {
            console.log(`[AudioToCameraLink] Scheduling action '${actionName}' in ${delayMs}ms`);
        }

        // Programmer la nouvelle action
        this.cameraState.actionTimer = setTimeout(() => {
            this.cameraState.actionTimer = null;
            if (this.config.debug.verbose) {
                console.log(`[AudioToCameraLink] Executing scheduled action '${actionName}'`);
            }
            action();
        }, delayMs);
    }

    // ============================================================================
    // MODE PLEIN ÉCRAN (FULLSCREEN)
    // ============================================================================

    /**
     * Grouper les micros actifs par caméra associée
     */
    private getActiveCameraGroups(): Map<string, string[]> {
        const cameraGroups = new Map<string, string[]>();

        // Parcourir tous les micros actifs
        for (const [micName, audioState] of this.audioStates.entries()) {
            if (audioState.level === 'high') {
                // Trouver la caméra associée à ce micro
                const mapping = this.config.micToCameraMapping.find((m: MicToCameraMapping) => m.micName === micName);
                if (mapping) {
                    const cameraSource = mapping.cameraSource;
                    if (!cameraGroups.has(cameraSource)) {
                        cameraGroups.set(cameraSource, []);
                    }
                    cameraGroups.get(cameraSource)!.push(micName);
                }
            }
        }

        return cameraGroups;
    }

    /**
     * Vérifier si on doit passer en mode plein écran
     */
    private shouldEnterFullscreen(activeCameraGroups: Map<string, string[]>): boolean {
        if (!this.config.fullscreenMode.enabled) {
            return false;
        }

        // Compter le nombre de caméras différentes qui ont des micros actifs
        const activeCameraCount = activeCameraGroups.size;

        return activeCameraCount >= this.config.fullscreenMode.minActiveMics;
    }

    /**
     * Gérer le mode plein écran - Version avec maintien minimum
     */
    private handleFullscreenMode(activeCameraGroups: Map<string, string[]>): void {
        const shouldBeFullscreen = this.shouldEnterFullscreen(activeCameraGroups);
        const now = Date.now();

        if (shouldBeFullscreen && !this.cameraState.isFullscreen) {
            // Démarrer le timer pour le mode plein écran si pas déjà démarré
            if (!this.cameraState.fullscreenTimer) {
                if (this.config.debug.verbose) {
                    console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Multiple cameras active, starting fullscreen timer (${this.config.fullscreenMode.multiMicDurationMs}ms)`);
                }

                this.cameraState.fullscreenTimer = setTimeout(() => {
                    // Vérifier à nouveau si on devrait toujours être en plein écran
                    const currentActiveCameraGroups = this.getActiveCameraGroups();
                    if (this.shouldEnterFullscreen(currentActiveCameraGroups)) {
                        if (this.config.debug.verbose) {
                            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Fullscreen timer expired, entering fullscreen mode`);
                        }
                        this.enterFullscreenMode();
                    } else {
                        if (this.config.debug.verbose) {
                            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Fullscreen timer expired but conditions no longer met, canceling`);
                        }
                        this.cameraState.fullscreenTimer = null;
                    }
                }, this.config.fullscreenMode.multiMicDurationMs);
            }
        } else if (!shouldBeFullscreen) {
            // Annuler le timer de plein écran si les conditions ne sont plus remplies
            if (this.cameraState.fullscreenTimer) {
                clearTimeout(this.cameraState.fullscreenTimer);
                this.cameraState.fullscreenTimer = null;
                if (this.config.debug.verbose) {
                    console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Fullscreen timer canceled (conditions no longer met)`);
                }
            }

            // Sortir du mode plein écran SEULEMENT si le délai minimum est écoulé
            if (this.cameraState.isFullscreen) {
                const timeSinceFullscreenStart = now - this.cameraState.fullscreenActivatedAt;

                if (timeSinceFullscreenStart >= this.config.fullscreenMode.minHoldDurationMs) {
                    if (this.config.debug.verbose) {
                        console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Multiple cameras no longer active, exiting fullscreen mode (held for ${timeSinceFullscreenStart}ms)`);
                    }
                    this.exitFullscreenMode(activeCameraGroups);
                } else {
                    const remainingHoldTime = this.config.fullscreenMode.minHoldDurationMs - timeSinceFullscreenStart;
                    if (this.config.debug.verbose) {
                        console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Fullscreen mode must be held for ${remainingHoldTime}ms more`);
                    }

                    // Programmer une réévaluation après le délai minimum
                    this.scheduleAction(() => {
                        this.evaluateCameraState();
                    }, remainingHoldTime + 100, 'reevaluate_fullscreen_exit');
                }
            }
        }
    }

    /**
     * Entrer en mode plein écran
     */
    private enterFullscreenMode(): void {
        if (this.cameraState.isFullscreen) return;

        const now = Date.now();
        this.cameraState.isFullscreen = true;
        this.cameraState.fullscreenActivatedAt = now;
        this.cameraState.fullscreenTimer = null; // Timer terminé

        if (this.config.debug.verbose) {
            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Entering fullscreen mode (multiple cameras active)`);
        }

        // Masquer toutes les caméras pour révéler CAM1
        void this.hideAllCameras();
    }

    /**
     * Sortir du mode plein écran
     */
    private exitFullscreenMode(activeCameraGroups: Map<string, string[]>): void {
        if (!this.cameraState.isFullscreen) return;

        const holdDuration = Date.now() - this.cameraState.fullscreenActivatedAt;
        this.cameraState.isFullscreen = false;
        this.cameraState.fullscreenActivatedAt = 0;

        if (this.config.debug.verbose) {
            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Exiting fullscreen mode (held for ${holdDuration}ms)`);
        }

        // Activer la caméra appropriée selon les micros encore actifs
        if (activeCameraGroups.size === 1) {
            const [cameraSource] = activeCameraGroups.keys();
            void this.activateCamera(cameraSource);
        }
    }

    // ============================================================================
    // UTILITAIRES
    // ============================================================================

    /**
     * Créer un timestamp formaté pour les logs
     */
    private getTimestamp(): string {
        const now = new Date();
        return now.toLocaleTimeString('fr-FR', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            fractionalSecondDigits: 3,
        });
    }

    /**
     * Nettoyer tous les timers actifs - Version simplifiée
     */
    private clearAllTimers(): void {
        if (this.cameraState.actionTimer) {
            clearTimeout(this.cameraState.actionTimer);
            this.cameraState.actionTimer = null;
        }
        if (this.cameraState.fullscreenTimer) {
            clearTimeout(this.cameraState.fullscreenTimer);
            this.cameraState.fullscreenTimer = null;
        }
    }
}
