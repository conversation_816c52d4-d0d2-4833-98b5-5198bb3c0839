/**
 * Interface pour les liens entre modules
 * Chaque fichier de liaison implémente cette interface
 */

import { IModule } from './module.interface';

/**
 * Interface de base pour un lien entre modules
 */
export interface IModuleLink {
    /**
     * Nom du lien (pour identification)
     */
    readonly name: string;

    /**
     * Description du lien
     */
    readonly description: string;

    /**
     * Indique si le lien est activé
     */
    readonly enabled: boolean;

    /**
     * Initialiser le lien entre les modules
     */
    initialize(modules: Map<string, IModule>): Promise<void>;

    /**
     * Nettoyer le lien (appelé lors de l'arrêt)
     */
    cleanup(): Promise<void>;
}

/**
 * Configuration d'un lien
 */
export interface LinkConfig {
    enabled: boolean;
    description?: string;
    options?: Record<string, any>;
}

/**
 * Registre des liens disponibles
 */
export interface LinkRegistry {
    [linkName: string]: {
        linkClass: new (config: LinkConfig) => IModuleLink;
        defaultConfig: LinkConfig;
    };
}
