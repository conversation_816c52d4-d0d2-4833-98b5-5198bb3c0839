import * as fs from 'fs';
import * as path from 'path';

/**
 * Générateur de fichiers de configuration locaux
 * Auto-génère les fichiers *.local.ts s'ils n'existent pas
 */
export class ConfigGenerator {
    private static readonly CUSTOM_DIR = (() => {
        // Détecter si nous sommes en mode dev (src/) ou build (dist/)
        const currentDir = __dirname;
        if (currentDir.includes('dist')) {
            // Mode production : fichiers dans src/config/custom depuis dist/config
            return path.join(__dirname, '..', '..', 'src', 'config', 'custom');
        } else {
            // Mode développement : fichiers dans le même répertoire
            return path.join(__dirname, 'custom');
        }
    })();

    /**
     * Génère tous les fichiers de configuration personnalisés manquants
     * Retourne true si des fichiers ont été générés (première fois)
     */
    static generateMissingCustomConfigs(): boolean {
        this.ensureCustomDirExists();

        const gabinGenerated = this.generateGabinCustomConfig();
        const companionGenerated = this.generateCompanionCustomConfig();
        const obsGenerated = this.generateOBSCustomConfig();
        const modulesGenerated = this.generateModulesCustomConfig();
        const audioToCameraGenerated = this.generateAudioToCameraCustomConfig();

        const isFirstTime = gabinGenerated || companionGenerated || obsGenerated || modulesGenerated || audioToCameraGenerated;

        if (isFirstTime) {
            this.showFirstTimeMessageAndExit();
        }

        return isFirstTime;
    }

    /**
     * S'assure que le dossier custom/ existe
     */
    private static ensureCustomDirExists(): void {
        if (!fs.existsSync(this.CUSTOM_DIR)) {
            fs.mkdirSync(this.CUSTOM_DIR, { recursive: true });
            console.log('[Config] Created custom config directory');
        }
    }

    /**
     * Génère gabin.custom.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateGabinCustomConfig(): boolean {
        const filePath = path.join(this.CUSTOM_DIR, 'gabin.custom.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getGabinCustomTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated gabin.custom.ts');
            return true;
        }
        return false;
    }

    /**
     * Génère companion.custom.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateCompanionCustomConfig(): boolean {
        const filePath = path.join(this.CUSTOM_DIR, 'companion.custom.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getCompanionCustomTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated companion.custom.ts');
            return true;
        }
        return false;
    }

    /**
     * Génère obs.custom.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateOBSCustomConfig(): boolean {
        const filePath = path.join(this.CUSTOM_DIR, 'obs.custom.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getOBSCustomTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated obs.custom.ts');
            return true;
        }
        return false;
    }

    /**
     * Génère modules.custom.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateModulesCustomConfig(): boolean {
        const filePath = path.join(this.CUSTOM_DIR, 'modules.custom.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getModulesCustomTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated modules.custom.ts');
            return true;
        }
        return false;
    }

    /**
     * Génère audio-to-camera.custom.ts s'il n'existe pas
     * Retourne true si le fichier a été généré
     */
    private static generateAudioToCameraCustomConfig(): boolean {
        const filePath = path.join(this.CUSTOM_DIR, 'audio-to-camera.custom.ts');

        if (!fs.existsSync(filePath)) {
            const content = this.getAudioToCameraCustomTemplate();
            fs.writeFileSync(filePath, content, 'utf8');
            console.log('[Config] Generated audio-to-camera.custom.ts');
            return true;
        }
        return false;
    }

    /**
     * Affiche le message de première configuration et arrête l'application immédiatement
     */
    private static showFirstTimeMessageAndExit(): void {
        console.log('\n' + '='.repeat(80));
        console.log('🎉 CONFIGURATION INITIALE TERMINÉE');
        console.log('='.repeat(80));
        console.log('');
        console.log('📁 Les fichiers de configuration personnalisés ont été générés :');
        console.log('   • src/config/custom/gabin.custom.ts');
        console.log('   • src/config/custom/companion.custom.ts');
        console.log('   • src/config/custom/obs.custom.ts');
        console.log('   • src/config/custom/modules.custom.ts');
        console.log('   • src/config/custom/audio-to-camera.custom.ts');
        console.log('');
        console.log('🔧 PROCHAINES ÉTAPES :');
        console.log('   1. Personnalisez vos configurations dans les fichiers custom');
        console.log('   2. Activez les modules souhaités dans modules.custom.ts');
        console.log("   3. Redémarrez l'application : npm run start");
        console.log('');
        console.log('💡 CONSEILS :');
        console.log('   • Les fichiers custom sont ignorés par Git');
        console.log('   • Modifiez directement les valeurs selon vos besoins');
        console.log("   • Consultez le README.md pour plus d'informations");
        console.log('');
        console.log('='.repeat(80));
        console.log('');
        console.log('🛑 Application arrêtée pour configuration. Redémarrez après personnalisation.');

        // Arrêt immédiat sans délai
        process.exit(0);
    }

    /**
     * Template pour gabin.custom.ts
     */
    private static getGabinCustomTemplate(): string {
        return `import { GabinConfig } from '../../gabin/gabin.types';

export const gabinCustomConfig: GabinConfig = {
    network: {
        listenPort: 33123,
        sendPort: 32123,
        sendHost: '127.0.0.1',
    },
    timing: {
        pingInterval: 5000,
        pingTimeout: 3000,
        reconnectionDelay: 500,
    },
    registrations: [
        { type: 'autocam', path: '/autocam' },
        { type: 'micFeedback', path: '/mic/MIC1' },
        { type: 'micFeedback', path: '/mic/MIC2' },
    ],
};
`;
    }

    /**
     * Template pour companion.custom.ts
     */
    private static getCompanionCustomTemplate(): string {
        return `import { CompanionConfig } from '../../companion/companion.types';

export const companionCustomConfig: CompanionConfig = {
    network: {
        listenPort: 33223,
        host: '127.0.0.1',
        port: 33224,
    },
    timing: {
        connectionCheckInterval: 6000,
        initialStateDelay: 1000,
    },
    feedbacks: [
        {
            type: 'autocam',
            path: '/autocam',
        },
        {
            type: 'mic',
            path: '/mic/MIC1',
            micName: 'MIC1',
        },
        {
            type: 'mic',
            path: '/mic/MIC2',
            micName: 'MIC2',
        },
    ],
};
`;
    }

    /**
     * Template pour obs.custom.ts
     */
    private static getOBSCustomTemplate(): string {
        return `import { OBSConfig } from '../../obs/obs.types';

export const obsCustomConfig: OBSConfig = {
    network: {
        host: '127.0.0.1',
        port: 4455,
        password: undefined,
    },
    timing: {
        connectionTimeout: 5000,
        reconnectInterval: 10000,
        pingInterval: 30000,
    },
    autoConnect: true,
};
`;
    }

    /**
     * Template pour modules.custom.ts
     */
    private static getModulesCustomTemplate(): string {
        return `export interface ModulesConfig {
    gabin: {
        enabled: boolean;
        description: string;
    };
    obs: {
        enabled: boolean;
        description: string;
    };
    companion: {
        enabled: boolean;
        description: string;
    };
}

export const modulesCustomConfig: ModulesConfig = {
    gabin: {
        enabled: false,
        description: 'Module Gabin pour caméras automatiques et gestion des micros',
    },
    obs: {
        enabled: false,
        description: 'Module OBS Studio pour contrôle via WebSocket',
    },
    companion: {
        enabled: false,
        description: 'Module Companion pour surfaces de contrôle OSC',
    },
};
`;
    }

    /**
     * Template pour audio-to-camera.custom.ts
     */
    private static getAudioToCameraCustomTemplate(): string {
        return `export interface MicToCameraMapping {
    micName: string;
    cameraSource: string;
}

export interface AudioToCameraConfig {
    enabled: boolean;
    targetScene: string;
    audioThresholdDb: number;
    activationDelayMs: number;
    cameraMinHoldMs: number;
    micToCameraMapping: MicToCameraMapping[];
    fallbackBehavior: {
        hideAllWhenInactive: boolean;
        hideDelayMs: number;
        fullscreenToSingleDelayMs: number;
        inactiveToSingleDelayMs: number;
    };
    fullscreenMode: {
        enabled: boolean;
        triggerDelayMs: number;
        minActiveMics: number;
        minHoldMs: number;
    };
    audioStabilization: {
        enabled: boolean;
        fluctuationWindowMs: number;
        minStableDurationMs: number;
    };
    debug: {
        verbose: boolean;
        logAudioLevels: boolean;
        logAudioThresholds: boolean;
        audioLogIntervalMs: number;
    };
}

export const audioToCameraCustomConfig: AudioToCameraConfig = {
    enabled: false,
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,
    activationDelayMs: 100,
    cameraMinHoldMs: 4000,
    micToCameraMapping: [
        { micName: 'mic_invite1', cameraSource: 'CAM 1' },
        { micName: 'mic_invite2', cameraSource: 'CAM 2' },
        { micName: 'mic_invite3', cameraSource: 'CAM 3' },
    ],
    fallbackBehavior: {
        hideAllWhenInactive: true,
        hideDelayMs: 3000,
        fullscreenToSingleDelayMs: 1500,
        inactiveToSingleDelayMs: 1500,
    },
    fullscreenMode: {
        enabled: true,
        triggerDelayMs: 3000,
        minActiveMics: 2,
        minHoldMs: 2000,
    },
    audioStabilization: {
        enabled: true,
        fluctuationWindowMs: 3000,
        minStableDurationMs: 1000,
    },
    debug: {
        verbose: false,
        logAudioLevels: false,
        logAudioThresholds: true,
        audioLogIntervalMs: 2000,
    },
};
`;
    }
}
