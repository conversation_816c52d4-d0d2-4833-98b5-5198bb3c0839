import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { IModule } from '../interfaces/module.interface';
import { IModuleLink, LinkConfig } from '../interfaces/link.interface';

/**
 * Gestionnaire des liens entre modules
 * Responsabilité : Orchestrer les communications inter-modules via des fichiers de liaison
 */
@Injectable()
export class LinkManagerService implements OnModuleInit, OnModuleDestroy {
    private modules = new Map<string, IModule>();
    private links = new Map<string, IModuleLink>();
    private linkConfigs = new Map<string, LinkConfig>();

    constructor() {
        // Configuration des liens disponibles
        this.initializeLinkConfigs();
    }

    async onModuleInit() {
        // Ne pas initialiser automatiquement - attendre l'enregistrement des modules
        console.log('[LinkManager] LinkManager ready - waiting for module registration');
    }

    /**
     * Initialiser les liens après l'enregistrement des modules
     */
    async initializeLinks(): Promise<void> {
        console.log('[LinkManager] Initializing module links...');
        await this.initializeActiveLinks();
    }

    async onModuleDestroy() {
        console.log('[LinkManager] Cleaning up module links...');
        await this.cleanupAllLinks();
    }

    /**
     * Enregistrer un module dans le gestionnaire
     */
    registerModule(module: IModule): void {
        console.log(`[LinkManager] Registering module: ${module.name}`);
        this.modules.set(module.name, module);
    }

    /**
     * Obtenir un module par son nom
     */
    getModule<T extends IModule = IModule>(name: string): T | undefined {
        return this.modules.get(name) as T;
    }

    /**
     * Obtenir tous les modules enregistrés
     */
    getAllModules(): Map<string, IModule> {
        return new Map(this.modules);
    }

    /**
     * Obtenir l'état de tous les modules
     */
    getModulesStatus(): Record<string, any> {
        const status: Record<string, any> = {};

        for (const [name, module] of this.modules) {
            status[name] = {
                enabled: module.enabled,
                connection: module.getConnectionStatus(),
                state: module.getState(),
            };
        }

        return status;
    }

    /**
     * Obtenir l'état de tous les liens
     */
    getLinksStatus(): Record<string, any> {
        const status: Record<string, any> = {};

        for (const [linkName, config] of this.linkConfigs) {
            status[linkName] = {
                enabled: config.enabled,
                description: config.description,
                options: config.options,
                active: this.links.has(linkName),
            };
        }

        return status;
    }

    /**
     * Initialiser la configuration des liens
     */
    private initializeLinkConfigs(): void {
        // Configuration des liens disponibles
        // Chaque lien peut être activé/désactivé individuellement

        this.linkConfigs.set('gabin-to-companion', {
            enabled: false,
            description: 'Synchronise les états Gabin vers Companion pour les feedbacks',
            options: {
                syncAutocam: true,
                syncMics: true,
            },
        });

        this.linkConfigs.set('companion-to-gabin', {
            enabled: false,
            description: 'Transmet les commandes Companion vers Gabin',
            options: {
                allowAutocamControl: true,
                allowMicControl: true,
            },
        });

        this.linkConfigs.set('audio-to-camera', {
            enabled: true, // Activé par défaut - contrôlé par la config audio-to-camera
            description: 'Contrôle la visibilité des caméras selon les niveaux audio des micros',
            options: {
                // Les options sont gérées par audioToCameraConfig
            },
        });

        console.log(`[LinkManager] Configured ${this.linkConfigs.size} available links`);
    }

    /**
     * Initialiser les liens actifs
     */
    private async initializeActiveLinks(): Promise<void> {
        for (const [linkName, config] of this.linkConfigs) {
            if (config.enabled) {
                try {
                    await this.initializeLink(linkName, config);
                } catch (error) {
                    console.error(`[LinkManager] Failed to initialize link '${linkName}':`, error.message);
                }
            } else {
                //console.log(`[LinkManager] Link '${linkName}' is disabled`);
            }
        }
    }

    /**
     * Initialiser un lien spécifique
     */
    private async initializeLink(linkName: string, config: LinkConfig): Promise<void> {
        console.log(`[LinkManager] Initializing link: ${linkName}`);

        try {
            let linkInstance: IModuleLink | null = null;

            // Créer l'instance du lien selon son nom
            switch (linkName) {
                case 'companion-to-gabin':
                    const { CompanionToGabinLink } = await import('../links/companion-to-gabin.link');
                    linkInstance = new CompanionToGabinLink(config);
                    break;

                case 'gabin-to-companion':
                    const { GabinToCompanionLink } = await import('../links/gabin-to-companion.link');
                    linkInstance = new GabinToCompanionLink(config);
                    break;

                case 'audio-to-camera':
                    const { AudioToCameraLink } = await import('../links/audio-to-camera.link');
                    linkInstance = new AudioToCameraLink(config);
                    break;

                default:
                    console.warn(`[LinkManager] Unknown link type: ${linkName}`);
                    return;
            }

            if (linkInstance) {
                // Initialiser le lien avec les modules disponibles
                await linkInstance.initialize(this.modules);
                this.links.set(linkName, linkInstance);
                console.log(`[LinkManager] Link '${linkName}' initialized successfully`);
            }
        } catch (error) {
            console.error(`[LinkManager] Failed to initialize link '${linkName}':`, error.message);
        }
    }

    /**
     * Nettoyer tous les liens
     */
    private async cleanupAllLinks(): Promise<void> {
        for (const [linkName, link] of this.links) {
            try {
                await link.cleanup();
                console.log(`[LinkManager] Cleaned up link: ${linkName}`);
            } catch (error) {
                console.error(`[LinkManager] Error cleaning up link '${linkName}':`, error.message);
            }
        }
        this.links.clear();
    }

    /**
     * Activer/désactiver un lien
     */
    async toggleLink(linkName: string, enabled: boolean): Promise<void> {
        const config = this.linkConfigs.get(linkName);
        if (!config) {
            throw new Error(`Link '${linkName}' not found`);
        }

        config.enabled = enabled;

        if (enabled) {
            await this.initializeLink(linkName, config);
        } else {
            const link = this.links.get(linkName);
            if (link) {
                await link.cleanup();
                this.links.delete(linkName);
            }
        }

        console.log(`[LinkManager] Link '${linkName}' ${enabled ? 'enabled' : 'disabled'}`);
    }
}
